'use client'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useQuery } from '@/store/queryContext/useQueryContext'
import { Root } from '@radix-ui/react-select'
import { X } from 'lucide-react'
import { useEffect, useState } from 'react'
import { Button } from '../ui/button'
import { Label } from '../ui/label'
import { useTranslations, useLocale } from 'next-intl'

export interface FilterSelectProps<T> extends React.ComponentProps<typeof Root> {
  label?: string
  name: string
  data: T[]
  placeholder?: string
  valueKey: keyof T & string
  labelKey: keyof T & string
}

const FilterSelect = <T,>({ label, name, data, valueKey, labelKey, placeholder, ...props }: FilterSelectProps<T>) => {
  const { forwardAddQuery, forwardDeleteQuery, forwardQuery } = useQuery()
  const [selectedValue, setSelectedValue] = useState<string | null>(forwardQuery?.[name] ?? null)
  const t = useTranslations()
  const isRtl = useLocale() === 'ar'

  useEffect(() => {
    setSelectedValue(forwardQuery?.[name] ?? null)
  }, [forwardQuery, name])

  const handleSelectFilter = (value: string) => {
    setSelectedValue(value)
    forwardAddQuery({ [name]: value })
  }

  const handleRemoveFilter = () => {
    setSelectedValue(null)
    forwardDeleteQuery(name)
  }

  return (
    <>
      {label && (
        <Label className="text-[22px] text-primary-02" htmlFor={`${name}-select`}>
          {label}
        </Label>
      )}
      <div className="relative">
        <Select value={selectedValue ?? ''} onValueChange={handleSelectFilter} {...props}>
          <SelectTrigger className="w-full" id={`${name}-select`} dir={isRtl ? 'rtl' : 'ltr'}>
            <SelectValue placeholder={placeholder ?? t('label.select_option')} />
          </SelectTrigger>
          <SelectContent className="bg-white text-gray-01 !p-0 !text-lg" dir={isRtl ? 'rtl' : 'ltr'}>
            {data.map((item, index) => (
              <SelectItem
                className="text-gray-01 text-lg"
                key={`${item[valueKey]}-${index}`}
                value={String(item[valueKey])}
              >
                {String(item[labelKey])}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {selectedValue && (
          <Button
            variant="ghost"
            type="button"
            onClick={handleRemoveFilter}
            tabIndex={-1}
            className="absolute end-4 top-1/2 -translate-y-1/2 z-10 !p-2 text-muted-foreground hover:bg-transparent w-fit"
            aria-label="Remove filter"
          >
            <X className="size-4" color="#A7A9AC" />
          </Button>
        )}
      </div>
    </>
  )
}

export default FilterSelect

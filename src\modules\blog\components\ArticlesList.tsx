import Article from '@/components/shared/Article'

const ArticlesList = () => {
  const articles = {
    items: [
      {
        id: 10,
        date: '02 September, 2025',
        title: 'Warning Signs of a Heart Attack',
        image: 'https://drdamaty-b.geexar.dev/storage/11/article-5.jpeg',
        link: null,
      },
      {
        id: 9,
        date: '02 September, 2025',
        title: 'Exercise and Heart Health',
        image: 'https://drdamaty-b.geexar.dev/storage/10/article-4.jpeg',
        link: 'https://www.youtube.com/watch?v=JWBtHBwSJoM',
      },
      {
        id: 8,
        date: '02 September, 2025',
        title: 'Latest Guidelines for Heart Patients',
        image: 'https://drdamaty-b.geexar.dev/storage/9/article-3.jpeg',
        link: 'https://www.youtube.com/watch?v=psg2dGpjqWo',
      },
      {
        id: 7,
        date: '02 September, 2025',
        title: 'Healthy Diet for a Healthy Heart',
        image: 'https://drdamaty-b.geexar.dev/storage/8/article-2.jpeg',
        link: null,
      },
      {
        id: 6,
        date: '02 September, 2025',
        title: 'Understanding High Blood Pressure',
        image: 'https://drdamaty-b.geexar.dev/storage/7/article-1.jpeg',
        link: null,
      },
    ],
  }
  return (
    <div className="grid lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-8">
      {articles.items.map((article) => (
        <Article key={article.id} {...article} title={article.title} image={article.image} date={article.date} />
      ))}
    </div>
  )
}

export default ArticlesList
